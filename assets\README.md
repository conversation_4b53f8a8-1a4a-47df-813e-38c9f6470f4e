# Icon Placeholder

This directory should contain application icons:

- `icon.png` (512x512) - Linux AppImage icon
- `icon.ico` - Windows executable icon  
- `icon.icns` - macOS application icon

For now, you can use any 512x512 PNG image as a placeholder.
The Electron builder will handle format conversion automatically.

## Icon Requirements

- **PNG Format**: 512x512 pixels for the base icon
- **Transparent Background**: Recommended for better integration
- **High Quality**: Icons will be scaled to various sizes automatically

## Converting Icons

Electron Builder can automatically convert PNG to other formats:
- PNG → ICO (Windows)
- PNG → ICNS (macOS)

Just provide a high-quality 512x512 PNG as `icon.png`.
