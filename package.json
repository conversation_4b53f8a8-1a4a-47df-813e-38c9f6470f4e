{"name": "bryzos-conn-troubleshoot", "version": "1.0.0", "description": "Bryzos Connectivity Troubleshooter - Desktop application for diagnosing network connectivity issues", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac-x64": "electron-builder --mac --x64", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "config:create": "node scripts/config-converter.js config.json", "make:win": "npx electron-builder --win --x64"}, "keywords": ["connectivity", "troubleshoot", "network", "diagnostics", "electron"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0"}, "dependencies": {"dns": "^0.2.2", "electron-squirrel-startup": "^1.0.1", "socket.io-client": "^4.8.1"}, "build": {"appId": "com.bryzos.conn-troubleshoot", "productName": "Bryzos Connectivity Troubleshooter", "directories": {"output": "dist"}, "extraResources": [{"from": "bz.config", "to": "bz.config"}], "files": ["src/**/*", "assets/**/*", "package.json"], "win": {"target": "squirrel", "icon": "assets/logo.ico"}, "mac": {"target": "dmg", "icon": "assets/logo.icns"}, "linux": {"target": "AppImage", "icon": "assets/logo.png"}}}