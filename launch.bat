@echo off
REM Bryzos Connectivity Troubleshooter - Launch Script for Windows

echo 🚀 Starting Bryzos Connectivity Troubleshooter...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies.
        pause
        exit /b 1
    )
)

REM Launch the application
echo 🔧 Launching application...
npm run dev

echo ✅ Application closed.
pause
