const https = require('https');
const http = require('http');
const dns = require('dns').promises;
const { URL } = require('url');
const os = require('os');
const tls = require('tls');

// Import our new endpoint configuration and testing modules
const { getEndpointConfiguration } = require('./config/endpoints-config');
const EndpointTester = require('./config/endpoint-tests');

class NetworkTests {
  constructor() {
    this.defaultTimeout = 10000; // 10 seconds
    this.defaultRetries = 3;
    this.endpointTester = new EndpointTester();
    this.endpoints = getEndpointConfiguration();
  }

  /**
   * Test connectivity using the new endpoint-based system
   * This replaces the old URL-based testing
   */
  async testConnectivity(options = {}) {
    const results = [];

    console.log(
      `Testing ${this.endpoints.length} endpoints from external configuration`
    );

    for (const endpoint of this.endpoints) {
      try {
        const result = await this.endpointTester.executeTest(endpoint);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          statusCode: null,
          responseTime: 0,
          error: `Test execution failed: ${error.message}`,
          endpoint: endpoint.displayName,
          subdomain: endpoint.subdomain,
        });
      }
    }

    return results;
  }

  /**
   * Test SSL certificates for HTTPS endpoints
   * Updated to work with the new endpoint system
   */
  async testCertificates() {
    const results = [];

    // Filter only HTTPS endpoints for certificate testing
    const httpsEndpoints = this.endpoints.filter((endpoint) =>
      endpoint.url.startsWith('https://')
    );

    for (const endpoint of httpsEndpoints) {
      const result = await this.testSingleCertificate(endpoint);
      results.push(result);
    }

    return results;
  }

  async testSingleCertificate(endpoint) {
    const result = {
      endpoint: endpoint.displayName,
      subdomain: endpoint.subdomain,
      valid: false,
      status: 'error',
      details: null,
      error: null,
    };

    try {
      const url = new URL(endpoint.url);

      if (url.protocol !== 'https:') {
        result.error = 'Not an HTTPS URL';
        return result;
      }

      const certInfo = await this.getCertificateInfo(
        url.hostname,
        url.port || 443
      );

      result.details = {
        subject: certInfo.subject.CN || 'Unknown',
        issuer: certInfo.issuer.CN || 'Unknown',
        expires: new Date(certInfo.valid_to).toLocaleDateString(),
        validFrom: new Date(certInfo.valid_from).toLocaleDateString(),
        serialNumber: certInfo.serialNumber,
        fingerprint: certInfo.fingerprint,
      };

      // Check if certificate is valid
      const now = new Date();
      const validFrom = new Date(certInfo.valid_from);
      const validTo = new Date(certInfo.valid_to);

      if (now < validFrom) {
        result.error = 'Certificate is not yet valid';
        result.status = 'warning';
      } else if (now > validTo) {
        result.error = 'Certificate has expired';
        result.status = 'error';
      } else {
        result.valid = true;
        result.status = 'success';

        // Check if expiring soon (within 30 days)
        const daysUntilExpiry = (validTo - now) / (1000 * 60 * 60 * 24);
        if (daysUntilExpiry <= 30) {
          result.status = 'warning';
          result.warning = `Certificate expires in ${Math.ceil(
            daysUntilExpiry
          )} days`;
        }
      }
    } catch (error) {
      result.error = error.message;
      result.status = 'error';
    }

    return result;
  }

  getCertificateInfo(hostname, port) {
    return new Promise((resolve, reject) => {
      const options = {
        host: hostname,
        port: port,
        rejectUnauthorized: false,
        requestCert: true,
      };

      const socket = tls.connect(options, () => {
        const cert = socket.getPeerCertificate();

        if (!cert || Object.keys(cert).length === 0) {
          reject(new Error('No certificate found'));
          return;
        }

        socket.end();
        resolve(cert);
      });

      socket.on('error', (error) => {
        reject(error);
      });

      socket.setTimeout(10000, () => {
        socket.destroy();
        reject(new Error('Certificate check timeout'));
      });
    });
  }

  async getSystemInfo() {
    const info = {
      // Basic system information
      platform: os.platform(),
      platformName: this.getPlatformName(os.platform()),
      arch: os.arch(),
      hostname: os.hostname(),
      uptime: os.uptime(),

      // OS version information
      osVersion: os.release(),
      osType: os.type(),

      // CPU information
      cpus: os.cpus(),
      cpuModel: os.cpus()[0]?.model || 'Unknown',
      cpuCores: os.cpus().length,

      // Memory information (in bytes, we'll format later)
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      usedMemory: os.totalmem() - os.freemem(),

      // Network interfaces
      networkInterfaces: os.networkInterfaces(),
      dns: [],
    };

    // Try to get DNS servers (platform-specific)
    try {
      if (os.platform() === 'win32') {
        // Windows DNS detection would require additional system calls
        info.dns = ['System Default'];
      } else {
        // Unix-like systems - try to read resolv.conf
        const fs = require('fs').promises;
        try {
          const resolvConf = await fs.readFile('/etc/resolv.conf', 'utf8');
          const dnsServers = resolvConf
            .split('\n')
            .filter((line) => line.startsWith('nameserver'))
            .map((line) => line.split(' ')[1])
            .filter((ip) => ip);

          info.dns = dnsServers.length > 0 ? dnsServers : ['System Default'];
        } catch {
          info.dns = ['System Default'];
        }
      }
    } catch (error) {
      info.dns = ['Unable to determine'];
    }

    // Add storage information
    try {
      info.storage = await this.getStorageInfo();
    } catch (error) {
      info.storage = { error: 'Unable to determine storage information' };
    }

    // Format memory information for display
    info.memoryFormatted = {
      total: this.formatBytes(info.totalMemory),
      used: this.formatBytes(info.usedMemory),
      free: this.formatBytes(info.freeMemory),
      usagePercentage: Math.round((info.usedMemory / info.totalMemory) * 100),
    };

    return info;
  }

  /**
   * Get human-readable platform name
   */
  getPlatformName(platform) {
    const platformNames = {
      win32: 'Windows',
      darwin: 'macOS',
      linux: 'Linux',
      freebsd: 'FreeBSD',
      openbsd: 'OpenBSD',
      aix: 'AIX',
      sunos: 'SunOS',
    };
    return platformNames[platform] || platform;
  }

  /**
   * Get storage/disk information
   */
  async getStorageInfo() {
    try {
      // For cross-platform disk space, we'll use a simple approach
      // Note: This is a basic implementation. For production, consider using a library like 'node-disk-info'
      if (os.platform() === 'win32') {
        // Windows - try to get disk space using system calls
        return await this.getWindowsStorageInfo();
      } else {
        // Unix-like systems - try to get disk space
        return await this.getUnixStorageInfo();
      }
    } catch (error) {
      return {
        error: 'Unable to determine storage information',
        details: error.message,
      };
    }
  }

  /**
   * Get Windows storage information
   */
  async getWindowsStorageInfo() {
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    try {
      // Use wmic to get disk space information
      const { stdout } = await execAsync(
        'wmic logicaldisk get size,freespace,caption'
      );
      const lines = stdout.trim().split('\n').slice(1); // Skip header

      const disks = [];
      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 3 && parts[0] && parts[1] && parts[2]) {
          const caption = parts[0];
          const freeSpace = parseInt(parts[1]);
          const totalSpace = parseInt(parts[2]);

          if (!isNaN(freeSpace) && !isNaN(totalSpace)) {
            disks.push({
              drive: caption,
              total: totalSpace,
              free: freeSpace,
              used: totalSpace - freeSpace,
              totalFormatted: this.formatBytes(totalSpace),
              freeFormatted: this.formatBytes(freeSpace),
              usedFormatted: this.formatBytes(totalSpace - freeSpace),
              usagePercentage: Math.round(
                ((totalSpace - freeSpace) / totalSpace) * 100
              ),
            });
          }
        }
      }

      return { disks, platform: 'Windows' };
    } catch (error) {
      return {
        error: 'Unable to get Windows storage info',
        details: error.message,
      };
    }
  }

  /**
   * Get Unix storage information
   */
  async getUnixStorageInfo() {
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    try {
      // Use df command to get disk space information
      const { stdout } = await execAsync('df -h');
      const lines = stdout.trim().split('\n').slice(1); // Skip header

      const disks = [];
      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 6) {
          const filesystem = parts[0];
          const total = parts[1];
          const used = parts[2];
          const available = parts[3];
          const usePercentage = parts[4];
          const mountPoint = parts[5];

          // Only include main filesystems (skip special ones)
          if (!filesystem.startsWith('/dev/') && !mountPoint.startsWith('/')) {
            continue;
          }

          disks.push({
            filesystem,
            mountPoint,
            total,
            used,
            available,
            usePercentage,
            usagePercentage: parseInt(usePercentage.replace('%', '')) || 0,
          });
        }
      }

      return { disks, platform: 'Unix' };
    } catch (error) {
      return {
        error: 'Unable to get Unix storage info',
        details: error.message,
      };
    }
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Test DNS resolution for all configured endpoints
   * Updated to work with the new endpoint system
   */
  async testDNS() {
    const results = [];

    // Extract unique hostnames from all endpoints
    const hostnames = [
      ...new Set(
        this.endpoints
          .map((endpoint) => {
            try {
              const url = new URL(endpoint.url);
              return url.hostname;
            } catch (error) {
              return null;
            }
          })
          .filter((hostname) => hostname)
      ),
    ];

    for (const hostname of hostnames) {
      const result = {
        hostname: hostname,
        success: false,
        addresses: [],
        error: null,
      };

      try {
        // Use DNS promises to resolve the hostname
        const addresses = await dns.resolve(hostname);

        result.success = true;
        result.addresses = addresses;
      } catch (error) {
        result.error = error.message;
      }

      results.push(result);
    }

    return results;
  }
}

// Export the class and instance
const networkTests = new NetworkTests();
module.exports = NetworkTests;
module.exports.networkTests = networkTests;
