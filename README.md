# Bryzos Connectivity Troubleshooter

A streamlined desktop application built with Electron to diagnose network connectivity issues. This tool focuses on testing connectivity to configured endpoints with comprehensive system information reporting.

## Features

- **🌐 Connectivity Testing**: Test HTTP/HTTPS/WebSocket connectivity to configured endpoints
- **🔒 Privacy-Focused**: URLs are hidden from end users, only test IDs and display names shown
- **📊 System Information**: Comprehensive system specs (OS, CPU, memory, storage, network)
- **⚡ Progress Tracking**: Real-time progress indicators with transparent overlay
- **📋 Smart Reporting**: Detailed reports with system context for troubleshooting
- **🔧 Environment Management**: Easy switching between dev/staging/demo/prod configurations
- **🎯 Simplified UI**: Clean interface focused on essential connectivity testing

## Installation

1. **Prerequisites**: Node.js 18+ and npm

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Run in development mode**:

   ```bash
   # Default (Production URLs)
   npm run dev

   # Test specific environments
   npm run dev:prod      # Production URLs
   npm run dev:staging   # Staging URLs
   npm run dev:demo      # Demo URLs
   npm run dev:dev       # Development URLs
   ```

4. **Build for production**:

   ```bash
   # Build for current platform
   npm run build

   # Build for specific platforms
   npm run build-win    # Windows
   npm run build-mac    # macOS
   npm run build-linux  # Linux
   ```

## Usage

### Quick Start

1. **Launch the application** using one of the environment-specific commands
2. **Run connectivity tests** - no manual configuration needed!
3. **Export reports** with comprehensive system information

### Single Configuration

The application uses a single external configuration file for maximum deployment flexibility:

```bash
npm run dev          # Launch application (uses bz.config)
```

### Running Tests

#### Dashboard Overview

- **Run All Tests**: Execute all connectivity tests with progress tracking
- **Test Connectivity**: Run individual connectivity tests
- **Export Report**: Generate comprehensive diagnostic reports with system info

#### Progress Tracking

- **🔒 Transparent Overlay**: Prevents user interaction during testing
- **📊 Real-time Progress**: Animated progress bar with status updates
- **⚡ Automatic Completion**: Tests complete automatically with results display
- **🎯 Status Updates**: "Starting tests..." → "Testing endpoints..." → "Completed!"

### Understanding Results

#### Test Display Format

- **🆔 Test ID**: Unique identifier for each endpoint (e.g., `widget-service-logout`)
- **📝 Display Name**: Human-readable service name (e.g., "Widget Service")
- **✅❌ Status**: Simple Success/Failed indicators
- **🔒 Privacy-Focused**: No URLs or sensitive endpoint information shown

#### Status Indicators

- **✅ Success**: Endpoint is reachable and responding correctly
- **❌ Failed**: Connection failed (check network connectivity or service availability)

#### Exported Reports Include

- **🧪 Test Results**: All connectivity test outcomes with test IDs
- **💻 OS Details**: Platform, version, architecture, hostname
- **🖥️ Hardware**: CPU model, cores, total/used memory
- **💾 Storage**: Disk space and usage across all drives/filesystems
- **🌐 Network**: DNS servers, network interfaces, IP addresses

## Configuration

### Single External Configuration

The application uses a single external configuration file for maximum deployment flexibility:

- **🚀 No Rebuilds**: Change endpoints without rebuilding the application
- **📦 Easy Deployment**: Just replace one config file on target machines
- **🔒 Secure**: Base64 encoding maintains URL privacy
- **🎯 Flexible**: Same application binary works everywhere

### Configuration Files Structure

```
bryzos-conn-troubleshoot/
├── bz.config                    # ← External Base64 configuration file
├── src/config/
│   ├── endpoints-config.js      # ← Reads from bz.config
│   └── endpoint-tests.js        # ← Test methods for each endpoint type
└── scripts/
    └── config-converter.js      # ← Creates bz.config from JSON
```

### Configuration Management

Create and deploy configurations easily:

```bash
# Use the ready-made config.json (easiest for developers)
npm run config:create

# Or create from a custom JSON file
node scripts/config-converter.js your-endpoints.json
```

### Quick Start for Developers

```bash
# Option 1: Use the ready-made config.json (recommended for development)
node scripts/config-converter.js config.json

# Option 2: Create your own endpoints JSON file
echo '[{"id":"test","displayName":"Test","url":"https://httpbin.org/get","method":"GET","testMethod":"testImageKitVideo"}]' > my-endpoints.json
node scripts/config-converter.js my-endpoints.json

# 3. Run the application
npm run dev
```

### Ready-to-Use Configuration

The project includes a `config.json` file with all current Bryzos endpoints that developers can use immediately:

- **Widget Service** - POST logout endpoint
- **ImageKit CDN** - Video file testing
- **S3 Storage** - File upload testing
- **WebSocket Service** - Real-time connection testing
- **CloudFront PDF** - PDF delivery testing
- **Imgix Thumbnail** - Image processing testing
- **CloudFront Video** - Video delivery testing
- **Vercel App** - Application deployment testing
- **TrueVault API** - Document storage testing

### Endpoint Configuration Format

Each endpoint configuration includes:

```json
{
  "id": "widget-service-logout",
  "displayName": "Widget Service",
  "subdomain": "prod-extended-widget-service.bryzosservices.com",
  "url": "https://prod-extended-widget-service.bryzosservices.com/user/logout",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "data": {
      "email_id": "<EMAIL>",
      "ui_version": "LOCAL_DEV"
    }
  },
  "testMethod": "testWidgetServiceLogout"
}
```

### Supported Test Methods

- **`testWidgetServiceLogout`**: POST request with JSON payload
- **`testImageKitVideo`**: HEAD request for CDN resources (optimized for large files)
- **`testS3Upload`**: PUT request for S3 storage (connection test only)
- **`testWebSocketService`**: Socket.IO connection test

### Deployment Workflow

1. **Create JSON**: Define your endpoints in a JSON file with the required format
2. **Generate Config**: Run `node scripts/config-converter.js your-endpoints.json`
3. **Deploy**: Copy the generated `bz.config` to your target machine's application directory
4. **Restart**: Application automatically loads the new configuration
5. **No Rebuild**: Same application binary works with any configuration

### Example JSON Configuration

```json
[
  {
    "id": "widget-service-logout",
    "displayName": "Widget Service",
    "subdomain": "prod-extended-widget-service.bryzosservices.com",
    "url": "https://prod-extended-widget-service.bryzosservices.com/user/logout",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json"
    },
    "body": {
      "data": {
        "email_id": "<EMAIL>",
        "ui_version": "LOCAL_DEV"
      }
    },
    "testMethod": "testWidgetServiceLogout"
  }
]
```

### Security Features

- **🔒 Base64 Encoding**: URLs are obfuscated in the external configuration file
- **🎭 Privacy-Focused**: Only test IDs and display names shown to users
- **📁 External Config**: Configuration separate from application code
- **🚫 No UI Access**: Users cannot see or modify endpoint configurations

## Troubleshooting

### Common Issues

1. **All tests failing**: Check network connectivity and DNS settings
2. **Connection timeouts**: May indicate firewall blocking or service unavailability
3. **WebSocket failures**: Corporate networks may block WebSocket connections
4. **Progress modal stuck**: Restart application if tests don't complete

### Corporate Networks

- The application handles corporate SSL inspection gracefully
- Self-signed certificates are accepted for connectivity testing
- WebSocket connections may be blocked by corporate firewalls
- Proxy settings are automatically detected where possible

### Getting Help

1. **Export Report**: Use the "Export Report" button to generate diagnostic information
2. **Check Console**: Open Developer Tools (F12) to view detailed error logs
3. **System Information**: Reports include comprehensive system details for troubleshooting

## Technical Details

### Architecture

- **Frontend**: HTML, CSS, JavaScript with Electron renderer process
- **Backend**: Node.js with Electron main process
- **IPC**: Secure communication between renderer and main processes
- **Testing**: Built-in Node.js modules (http, https, dns, tls, os) + Socket.IO client
- **Configuration**: Base64-encoded endpoint configurations for security

### File Structure

```
bryzos-conn-troubleshoot/
├── bz.config                    # External Base64 configuration file
├── src/
│   ├── main.js                  # Electron main process
│   ├── preload.js               # Secure IPC bridge
│   ├── network-tests.js         # Network testing logic with system info
│   ├── config/
│   │   ├── endpoints-config.js  # Reads from external bz.config
│   │   └── endpoint-tests.js    # Test methods for different endpoint types
│   └── renderer/
│       ├── index.html           # Main UI (simplified, connectivity-focused)
│       ├── styles.css           # Application styles with progress animations
│       └── app.js               # Frontend application logic
└── scripts/
    └── config-converter.js      # Creates bz.config from JSON
```

### Security Features

- **Context Isolation**: Enabled for secure renderer process
- **Node Integration**: Disabled in renderer for security
- **Secure IPC**: Safe communication between processes
- **URL Privacy**: Endpoints hidden from users, only test IDs shown
- **Base64 Encoding**: Configuration obfuscation
- **No External Dependencies**: Core functionality uses only Node.js built-ins

### Testing Capabilities

- **HTTP/HTTPS**: GET, POST, PUT requests with custom headers and payloads
- **WebSocket**: Socket.IO connection testing with timeout handling
- **System Information**: OS, CPU, memory, storage, and network details
- **Cross-Platform**: Windows, macOS, and Linux support
- **Progress Tracking**: Real-time progress with user interaction blocking

## Development

### Project Features

- **🎯 Simplified Focus**: Streamlined to connectivity testing only
- **🔒 Privacy-First**: URLs hidden from end users, only test IDs visible
- **⚡ Real-time Progress**: Animated progress bars with user interaction blocking
- **📊 Comprehensive Reports**: System information included in diagnostic exports
- **🌍 Multi-Environment**: Easy switching between dev/staging/demo/prod
- **🎨 Modern UI**: Clean design with smooth animations and responsive layout

### Key Technologies

- **Electron**: Cross-platform desktop application framework
- **Socket.IO Client**: WebSocket connectivity testing
- **Node.js Built-ins**: No external dependencies for core network testing
- **Base64 Encoding**: Configuration security and obfuscation
- **CSS Animations**: Progress indicators and modal transitions

### Color Palette

- **Rich Black**: #0d1b2aff (Primary backgrounds)
- **Oxford Blue**: #1b263bff (Secondary backgrounds)
- **Yinmn Blue**: #415a77ff (Primary accents)
- **Silver Lake Blue**: #778da9ff (Secondary accents)
- **Platinum**: #e0e1ddff (Light backgrounds)

### Development Workflow

1. **Configuration**: Create JSON file with endpoint definitions
2. **Generate Config**: Run `node scripts/config-converter.js your-endpoints.json`
3. **Testing**: Use `npm run dev` to test with current configuration
4. **Deployment**: Copy `bz.config` to target machines (no rebuild needed)
5. **Building**: Cross-platform builds available for Windows, macOS, Linux

## License

© 2024 Bryzos. All rights reserved.

This software is proprietary and confidential. Unauthorized copying, distribution, or use of this software, via any medium, is strictly prohibited without the express written permission of Bryzos.

## Support

### Diagnostic Information

The application provides comprehensive diagnostic capabilities:

1. **Export Reports**: Click "Export Report" to generate detailed diagnostic files including:

   - Connectivity test results with test IDs and display names
   - Complete system information (OS, CPU, memory, storage, network)
   - Timestamp and application version information
   - No sensitive URL information exposed

2. **Developer Console**: Press F12 to access detailed error logs and debugging information

3. **System Information**: Reports automatically include:
   - Operating system details and version
   - CPU model and core count
   - Memory usage and availability
   - Storage space across all drives
   - Network interface and DNS configuration

### Getting Help

For issues or questions:

1. **Check Troubleshooting**: Review the troubleshooting section above
2. **Export Diagnostic Report**: Use the built-in export functionality
3. **Check Console Logs**: Use Developer Tools for detailed error information
4. **Environment Testing**: Try different environments (dev/staging/prod) to isolate issues

### Report Contents

Exported reports contain:

- ✅ **Test Results**: Success/failure status with test IDs
- ✅ **System Specs**: Complete hardware and software information
- ✅ **Network Config**: DNS servers and interface details
- ✅ **Privacy Protected**: No URLs or sensitive endpoint information
- ✅ **Structured Data**: JSON format for easy analysis
