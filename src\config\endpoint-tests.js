// Bryzos Connectivity Troubleshooter - Endpoint-Specific Test Methods
// Individual test methods for each endpoint type

const https = require('https');
const http = require('http');
const io = require('socket.io-client');
const { URL } = require('url');

/**
 * Base test class with common functionality
 */
class EndpointTester {
  constructor() {
    this.defaultTimeout = 10000; // 10 seconds default timeout
  }

  /**
   * Determine if a status code represents a successful connection
   * Success: 200-299, 300-399 (redirects), 400-499 (except 404) - means we can reach the endpoint
   * Failure: 404, 500+, timeout, DNS errors
   */
  isSuccessfulConnection(statusCode) {
    if (statusCode >= 200 && statusCode < 300) return true; // Success
    if (statusCode >= 300 && statusCode < 400) return true; // Redirects - still reachable
    if (statusCode >= 400 && statusCode < 500 && statusCode !== 404)
      return true; // Client error but reachable
    return false; // 404, 500+, or other errors
  }

  /**
   * Create HTTP/HTTPS request options from endpoint configuration
   */
  createRequestOptions(endpoint) {
    const url = new URL(endpoint.url);
    const isHttps = url.protocol === 'https:';

    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname + url.search,
      method: endpoint.method || 'GET',
      timeout: this.defaultTimeout,
      headers: {
        'User-Agent': 'Bryzos-Connectivity-Troubleshooter/1.0',
        ...endpoint.headers,
      },
    };

    // For HTTPS, allow self-signed certificates (common in corporate environments)
    if (isHttps) {
      options.rejectUnauthorized = false;
    }

    return { options, isHttps };
  }

  /**
   * Test Widget Service Logout endpoint (POST with JSON payload)
   */
  async testWidgetServiceLogout(endpoint) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const { options, isHttps } = this.createRequestOptions(endpoint);

      const requestModule = isHttps ? https : http;
      const postData = JSON.stringify(endpoint.body);

      options.headers['Content-Length'] = Buffer.byteLength(postData);

      const req = requestModule.request(options, (res) => {
        const responseTime = Date.now() - startTime;
        const success = this.isSuccessfulConnection(res.statusCode);

        resolve({
          success,
          statusCode: res.statusCode,
          responseTime,
          error: success ? null : `HTTP ${res.statusCode}`,
          id: endpoint.id,
          endpoint: endpoint.displayName,
        });
      });

      req.on('error', (error) => {
        const responseTime = Date.now() - startTime;
        resolve({
          success: false,
          statusCode: null,
          responseTime,
          error: error.message,
          id: endpoint.id,
          endpoint: endpoint.displayName,
        });
      });

      req.on('timeout', () => {
        req.destroy();
        const responseTime = Date.now() - startTime;
        resolve({
          success: false,
          statusCode: null,
          responseTime,
          error: 'Connection timeout',
          id: endpoint.id,
          endpoint: endpoint.displayName,
        });
      });

      // Write POST data
      req.write(postData);
      req.end();
    });
  }

  /**
   * Test ImageKit CDN video endpoint (HEAD request)
   * Uses HEAD request for large video files to avoid downloading entire content
   */
  async testImageKitVideo(endpoint) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const { options, isHttps } = this.createRequestOptions(endpoint);

      // Use HEAD request for video files to avoid downloading large content
      options.method = 'HEAD';

      const requestModule = isHttps ? https : http;

      const req = requestModule.request(options, (res) => {
        const responseTime = Date.now() - startTime;
        const success = this.isSuccessfulConnection(res.statusCode);

        // For HEAD requests, there's no body to read - resolve immediately
        resolve({
          success,
          statusCode: res.statusCode,
          responseTime,
          error: success ? null : `HTTP ${res.statusCode}`,
          id: endpoint.id,
          endpoint: endpoint.displayName,
        });
      });

      req.on('error', (error) => {
        const responseTime = Date.now() - startTime;
        resolve({
          success: false,
          statusCode: null,
          responseTime,
          error: error.message,
          id: endpoint.id,
          endpoint: endpoint.displayName,
        });
      });

      req.on('timeout', () => {
        req.destroy();
        const responseTime = Date.now() - startTime;
        resolve({
          success: false,
          statusCode: null,
          responseTime,
          error: 'Connection timeout',
          id: endpoint.id,
          endpoint: endpoint.displayName,
        });
      });

      req.end();
    });
  }

  /**
   * Test S3 Upload endpoint (PUT request - just test connection, no actual upload)
   */
  async testS3Upload(endpoint) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const { options, isHttps } = this.createRequestOptions(endpoint);

      const requestModule = isHttps ? https : http;

      const req = requestModule.request(options, (res) => {
        const responseTime = Date.now() - startTime;
        // For S3, even auth errors (403) mean we can reach the endpoint
        const success =
          this.isSuccessfulConnection(res.statusCode) || res.statusCode === 403;

        resolve({
          success,
          statusCode: res.statusCode,
          responseTime,
          error: success ? null : `HTTP ${res.statusCode}`,
          id: endpoint.id,
          endpoint: endpoint.displayName,
        });
      });

      req.on('error', (error) => {
        const responseTime = Date.now() - startTime;
        resolve({
          success: false,
          statusCode: null,
          responseTime,
          error: error.message,
          id: endpoint.id,
          endpoint: endpoint.displayName,
        });
      });

      req.on('timeout', () => {
        req.destroy();
        const responseTime = Date.now() - startTime;
        resolve({
          success: false,
          statusCode: null,
          responseTime,
          error: 'Connection timeout',
          id: endpoint.id,
          endpoint: endpoint.displayName,
        });
      });

      req.end();
    });
  }

  /**
   * Test WebSocket Service endpoint (Socket.IO connection)
   */
  async testWebSocketService(endpoint) {
    return new Promise((resolve) => {
      const startTime = Date.now();

      try {
        const socket = io(endpoint.url, {
          transports: ['websocket'],
          timeout: 5000,
          rejectUnauthorized: false, // Allow self-signed certificates
        });

        socket.on('connect', () => {
          const responseTime = Date.now() - startTime;
          socket.disconnect();
          resolve({
            success: true,
            statusCode: null,
            responseTime,
            error: null,
            id: endpoint.id,
            endpoint: endpoint.displayName,
          });
        });

        socket.on('connect_error', (error) => {
          const responseTime = Date.now() - startTime;
          socket.disconnect();
          resolve({
            success: false,
            statusCode: null,
            responseTime,
            error: error.message || 'Socket.IO connection failed',
            id: endpoint.id,
            endpoint: endpoint.displayName,
          });
        });

        // Set timeout manually for Socket.IO
        setTimeout(() => {
          if (!socket.connected) {
            socket.disconnect();
            const responseTime = Date.now() - startTime;
            resolve({
              success: false,
              statusCode: null,
              responseTime,
              error: 'Socket.IO connection timeout',
              id: endpoint.id,
              endpoint: endpoint.displayName,
            });
          }
        }, 5000);
      } catch (error) {
        const responseTime = Date.now() - startTime;
        resolve({
          success: false,
          statusCode: null,
          responseTime,
          error: error.message,
          id: endpoint.id,
          endpoint: endpoint.displayName,
        });
      }
    });
  }

  /**
   * Execute test method for a given endpoint
   */
  async executeTest(endpoint) {
    const methodName = endpoint.testMethod;

    if (typeof this[methodName] !== 'function') {
      return {
        success: false,
        statusCode: null,
        responseTime: 0,
        error: `Test method '${methodName}' not found`,
        id: endpoint.id,
        endpoint: endpoint.displayName,
      };
    }

    try {
      return await this[methodName](endpoint);
    } catch (error) {
      return {
        success: false,
        statusCode: null,
        responseTime: 0,
        error: `Test execution failed: ${error.message}`,
        id: endpoint.id,
        endpoint: endpoint.displayName,
      };
    }
  }
}

module.exports = EndpointTester;
