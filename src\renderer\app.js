// Bryzos Connectivity Troubleshooter - Main Application Logic

// Use an IIFE to avoid global namespace pollution
(function () {
  // Handle any uncaught errors
  window.addEventListener('error', (event) => {
    console.error('Uncaught error:', event.error);
  });

  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
  });

  // Define the class inside the IIFE
  class ConnectivityTroubleshooter {
    constructor() {
      // Wait for DOM to be fully loaded
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => this.initialize());
      } else {
        this.initialize();
      }
    }

    initialize() {
      this.currentSection = 'dashboard';
      this.testResults = {
        connectivity: null,
        dns: null,
        certificates: null,
        system: null,
      };

      this.init();
    }

    init() {
      this.setupEventListeners();
      this.updateLastUpdated();
    }

    setupEventListeners() {
      // Navigation
      document.querySelectorAll('.nav-link').forEach((link) => {
        if (link) {
          link.addEventListener('click', (e) => {
            e.preventDefault();
            const section = e.target.closest('.nav-link').dataset.section;
            this.switchSection(section);
          });
        }
      });

      // Add event listeners safely
      this.addEventListenerSafely('run-all-tests', 'click', () =>
        this.runAllTests()
      );
      this.addEventListenerSafely('run-connectivity-tests', 'click', () =>
        this.runConnectivityTests()
      );
      this.addEventListenerSafely('quick-connectivity', 'click', () =>
        this.runConnectivityTests()
      );
      this.addEventListenerSafely('export-report', 'click', () =>
        this.exportReport()
      );
    }

    // Helper method to safely add event listeners
    addEventListenerSafely(elementId, eventType, callback) {
      const element = document.getElementById(elementId);
      if (element) {
        element.addEventListener(eventType, callback);
      }
    }

    switchSection(sectionName) {
      try {
        // Update navigation
        document.querySelectorAll('.nav-link').forEach((link) => {
          link.classList.remove('active');
        });

        const navLink = document.querySelector(
          `[data-section="${sectionName}"]`
        );
        if (navLink) {
          navLink.classList.add('active');
        } else {
          console.warn(`Navigation link for section ${sectionName} not found`);
        }

        // Update content sections
        document.querySelectorAll('.content-section').forEach((section) => {
          section.classList.remove('active');
        });

        const sectionElement = document.getElementById(
          `${sectionName}-section`
        );
        if (sectionElement) {
          sectionElement.classList.add('active');
        } else {
          console.warn(`Section element ${sectionName}-section not found`);
        }

        this.currentSection = sectionName;
      } catch (error) {
        console.error('Error switching sections:', error);
      }
    }

    async runAllTests() {
      this.showProgressModal();
      this.updateProgress(0, 'Starting connectivity tests...');

      try {
        // Test connectivity only
        this.updateProgress(50, 'Testing connectivity...');
        await this.runConnectivityTests(false);

        this.updateProgress(100, 'Tests completed!');

        setTimeout(() => {
          this.hideProgressModal();
          this.updateDashboard();
          this.updateLastUpdated();
        }, 1000);
      } catch (error) {
        console.error('Error running tests:', error);
        this.hideProgressModal();
        this.showError('Failed to run tests: ' + error.message);
      }
    }

    async runConnectivityTests(updateDashboard = true) {
      // Show progress modal for individual connectivity tests
      if (updateDashboard) {
        this.showProgressModal();
        this.updateProgress(0, 'Starting connectivity tests...');
      }

      try {
        if (updateDashboard) {
          this.updateProgress(50, 'Testing endpoints...');
        }

        // Use the new endpoint-based testing (no URLs parameter needed)
        const response = await window.electronAPI.testConnectivity();

        if (response.success) {
          this.testResults.connectivity = response.results;
          this.displayConnectivityResults(response.results);

          if (updateDashboard) {
            this.updateProgress(100, 'Tests completed!');
            setTimeout(() => {
              this.hideProgressModal();
              this.updateDashboard();
              this.updateLastUpdated();
            }, 500);
          }
        } else {
          if (updateDashboard) {
            this.hideProgressModal();
          }
          throw new Error(response.error);
        }
      } catch (error) {
        console.error('Connectivity test error:', error);
        if (updateDashboard) {
          this.hideProgressModal();
        }
        this.showError('Connectivity test failed: ' + error.message);
      }
    }

    async runDNSTests(updateDashboard = true) {
      try {
        // Use the new endpoint-based DNS testing (no hostnames parameter needed)
        const response = await window.electronAPI.testDNS();

        if (response && response.success) {
          this.testResults.dns = response.results;
          this.displayDNSResults(response.results);

          if (updateDashboard) {
            this.updateDashboard();
            this.updateLastUpdated();
          }
        } else {
          throw new Error(response ? response.error : 'Unknown DNS test error');
        }
      } catch (error) {
        console.error('DNS test error:', error);
        this.showError('DNS test failed: ' + error.message);
      }
    }

    async runCertificateTests(updateDashboard = true) {
      try {
        // Use the new endpoint-based certificate testing (no URLs parameter needed)
        const response = await window.electronAPI.testCertificates();

        if (response.success) {
          this.testResults.certificates = response.results;
          this.displayCertificateResults(response.results);

          if (updateDashboard) {
            this.updateDashboard();
            this.updateLastUpdated();
          }
        } else {
          throw new Error(response.error);
        }
      } catch (error) {
        console.error('Certificate test error:', error);
        this.showError('Certificate test failed: ' + error.message);
      }
    }

    async refreshSystemInfo(updateDashboard = true) {
      try {
        const response = await window.electronAPI.getSystemInfo();

        if (response.success) {
          this.testResults.system = response.info;
          this.displaySystemInfo(response.info);

          if (updateDashboard) {
            this.updateDashboard();
            this.updateLastUpdated();
          }
        } else {
          throw new Error(response.error);
        }
      } catch (error) {
        console.error('System info error:', error);
        this.showError('Failed to get system info: ' + error.message);
      }
    }

    displayConnectivityResults(results) {
      try {
        const container = document.getElementById('connectivity-results');

        if (!container) {
          console.error('Connectivity results container not found');
          return;
        }

        if (!results || results.length === 0) {
          container.innerHTML =
            '<p class="text-center">No connectivity results available.</p>';
          return;
        }

        const html = results
          .map(
            (result) => `
              <div class="test-item">
                <div class="test-info">
                  <div class="test-id">${result.id || 'unknown-test'}</div>
                  <div class="test-name">${
                    result.endpoint || 'Unknown Endpoint'
                  }</div>
                </div>
                <div class="test-status">
                  <span class="status-indicator ${
                    result.success ? 'success' : 'error'
                  }"></span>
                  <span class="test-status-text ${
                    result.success ? 'success' : 'error'
                  }">
                    ${result.success ? 'Success' : 'Failed'}
                  </span>
                </div>
              </div>
            `
          )
          .join('');

        container.innerHTML = html;
      } catch (error) {
        console.error('Error displaying connectivity results:', error);
      }
    }

    displayDNSResults(results) {
      try {
        const container = document.getElementById('dns-results');

        if (!container) {
          console.error('DNS results container not found');
          return;
        }

        if (!results || results.length === 0) {
          container.innerHTML =
            '<p class="text-center">No DNS results available.</p>';
          return;
        }

        const html = results
          .map(
            (result) => `
              <div class="test-item">
                <div class="test-name">${result.hostname}</div>
                <div class="test-status">
                  <span class="status-indicator ${
                    result.success ? 'success' : 'error'
                  }"></span>
                  <span class="test-status-text ${
                    result.success ? 'success' : 'error'
                  }">
                    ${result.success ? 'Resolved' : 'Failed'}
                  </span>
                </div>
              </div>
              ${
                result.addresses
                  ? `<div class="dns-details">
                  <p><strong>IP Addresses:</strong> ${result.addresses.join(
                    ', '
                  )}</p>
                </div>`
                  : ''
              }
              ${
                result.error
                  ? `<div class="test-error">${result.error}</div>`
                  : ''
              }
            `
          )
          .join('');

        container.innerHTML = html;
      } catch (error) {
        console.error('Error displaying DNS results:', error);
      }
    }

    displayCertificateResults(results) {
      const container = document.getElementById('certificate-results');

      if (!results || results.length === 0) {
        container.innerHTML =
          '<p class="text-center">No certificate results available.</p>';
        return;
      }

      const html = results
        .map(
          (result) => `
                        <div class="test-item">
                            <div class="test-name">${
                              result.endpoint || 'Unknown Endpoint'
                            }</div>
                            <div class="test-subdomain">${
                              result.subdomain || 'Unknown Domain'
                            }</div>
                            <div class="test-status">
                                <span class="status-indicator ${
                                  result.status
                                }"></span>
                                <span class="test-status-text ${result.status}">
                                    ${result.valid ? 'Valid' : 'Invalid'}
                                </span>
                            </div>
                        </div>
                        ${
                          result.details
                            ? `
                                <div class="certificate-details">
                                    <p><strong>Issuer:</strong> ${
                                      result.details.issuer || 'Unknown'
                                    }</p>
                                    <p><strong>Expires:</strong> ${
                                      result.details.expires || 'Unknown'
                                    }</p>
                                    <p><strong>Subject:</strong> ${
                                      result.details.subject || 'Unknown'
                                    }</p>
                                </div>
                            `
                            : ''
                        }
                        ${
                          result.error
                            ? `<div class="test-error">${result.error}</div>`
                            : ''
                        }
                    `
        )
        .join('');

      container.innerHTML = html;
    }

    displaySystemInfo(info) {
      const container = document.getElementById('system-info-results');

      if (!info) {
        container.innerHTML =
          '<p class="text-center">No system information available.</p>';
        return;
      }

      const html = `
                    <div class="system-info">
                        <h4>Network Interfaces</h4>
                        ${
                          info.networkInterfaces
                            ? Object.entries(info.networkInterfaces)
                                .map(
                                  ([name, interfaces]) => `
                        <div class="interface-group">
                            <h5>${name}</h5>
                            ${interfaces
                              .map(
                                (iface) => `
                                <div class="interface-item">
                                    <span>${iface.address} (${
                                  iface.family
                                })</span>
                                    <span class="status-indicator ${
                                      iface.internal ? 'warning' : 'success'
                                    }"></span>
                                </div>
                            `
                              )
                              .join('')}
                        </div>
                    `
                                )
                                .join('')
                            : 'No network interface information available'
                        }
                        
                        <h4>DNS Configuration</h4>
                        <p><strong>DNS Servers:</strong> ${
                          info.dns ? info.dns.join(', ') : 'Unknown'
                        }</p>
                        
                        <h4>System Details</h4>
                        <p><strong>Platform:</strong> ${
                          info.platform || 'Unknown'
                        }</p>
                        <p><strong>Architecture:</strong> ${
                          info.arch || 'Unknown'
                        }</p>
                        <p><strong>Hostname:</strong> ${
                          info.hostname || 'Unknown'
                        }</p>
                    </div>
                `;

      container.innerHTML = html;
    }

    updateDashboard() {
      // Update connectivity status
      const connectivityCard = document.getElementById('connectivity-status');
      const connectivityIndicator = document.getElementById(
        'connectivity-indicator'
      );
      const connectivityContent = document.getElementById(
        'connectivity-content'
      );

      if (this.testResults.connectivity) {
        const results = this.testResults.connectivity;
        const successCount = results.filter((r) => r.success).length;
        const totalCount = results.length;

        if (successCount === totalCount) {
          connectivityCard.className = 'status-card success';
          connectivityIndicator.className = 'status-indicator success';
          connectivityContent.textContent = `All ${totalCount} URLs are reachable`;
        } else if (successCount > 0) {
          connectivityCard.className = 'status-card warning';
          connectivityIndicator.className = 'status-indicator warning';
          connectivityContent.textContent = `${successCount}/${totalCount} URLs are reachable`;
        } else {
          connectivityCard.className = 'status-card error';
          connectivityIndicator.className = 'status-indicator error';
          connectivityContent.textContent = `No URLs are reachable`;
        }
      }

      // Update DNS status
      const dnsCard = document.getElementById('dns-status');
      const dnsIndicator = document.getElementById('dns-indicator');
      const dnsContent = document.getElementById('dns-content');

      if (this.testResults.dns) {
        const results = this.testResults.dns;
        const successCount = results.filter((r) => r.success).length;
        const totalCount = results.length;

        if (successCount === totalCount) {
          dnsCard.className = 'status-card success';
          dnsIndicator.className = 'status-indicator success';
          dnsContent.textContent = `All ${totalCount} domains resolve correctly`;
        } else if (successCount > 0) {
          dnsCard.className = 'status-card warning';
          dnsIndicator.className = 'status-indicator warning';
          dnsContent.textContent = `${successCount}/${totalCount} domains resolve correctly`;
        } else {
          dnsCard.className = 'status-card error';
          dnsIndicator.className = 'status-indicator error';
          dnsContent.textContent = `DNS resolution failed`;
        }
      }

      // Update certificate status
      const certificateCard = document.getElementById('certificate-status');
      const certificateIndicator = document.getElementById(
        'certificate-indicator'
      );
      const certificateContent = document.getElementById('certificate-content');

      if (this.testResults.certificates) {
        const results = this.testResults.certificates;
        const validCount = results.filter((r) => r.valid).length;
        const totalCount = results.length;

        if (validCount === totalCount) {
          certificateCard.className = 'status-card success';
          certificateIndicator.className = 'status-indicator success';
          certificateContent.textContent = `All ${totalCount} certificates are valid`;
        } else if (validCount > 0) {
          certificateCard.className = 'status-card warning';
          certificateIndicator.className = 'status-indicator warning';
          certificateContent.textContent = `${validCount}/${totalCount} certificates are valid`;
        } else {
          certificateCard.className = 'status-card error';
          certificateIndicator.className = 'status-indicator error';
          certificateContent.textContent = `Certificate validation failed`;
        }
      }

      // Update system status
      const systemCard = document.getElementById('system-status');
      const systemIndicator = document.getElementById('system-indicator');
      const systemContent = document.getElementById('system-content');

      if (this.testResults.system) {
        systemCard.className = 'status-card success';
        systemIndicator.className = 'status-indicator success';
        systemContent.textContent = `System information loaded`;
      }
    }

    showProgressModal() {
      document.getElementById('progress-modal').style.display = 'flex';
    }

    hideProgressModal() {
      document.getElementById('progress-modal').style.display = 'none';
    }

    updateProgress(percentage, text) {
      document.getElementById('progress-fill').style.width = percentage + '%';
      document.getElementById('progress-text').textContent = text;
    }

    updateLastUpdated() {
      const now = new Date();
      const timeString = now.toLocaleTimeString();
      document.getElementById(
        'last-updated'
      ).textContent = `Last updated: ${timeString}`;
    }

    showError(message) {
      // Simple error display - could be enhanced with a proper modal
      alert('Error: ' + message);
    }

    showSuccess(message) {
      // Simple success display - could be enhanced with a proper modal
      alert('Success: ' + message);
    }

    async exportReport() {
      // Get system information for the report
      let systemInfo = null;
      try {
        const response = await window.electronAPI.getSystemInfo();
        if (response.success) {
          systemInfo = response.info;
        }
      } catch (error) {
        console.error('Failed to get system info for report:', error);
      }

      const report = {
        timestamp: new Date().toISOString(),
        appInfo: {
          name: 'Bryzos Connectivity Troubleshooter',
          version: window.appInfo?.version || '1.0.0',
          platform: window.appInfo?.platform || 'unknown',
        },
        systemInfo: systemInfo,
        results: {
          connectivity: this.testResults.connectivity,
        },
      };

      try {
        const response = await window.electronAPI.exportReport(report);
        if (response.success) {
          this.showSuccess('Report exported successfully!');
        } else {
          this.showError('Failed to export report: ' + response.error);
        }
      } catch (error) {
        console.error('Export error:', error);
        this.showError('Failed to export report: ' + error.message);
      }
    }
  }

  // Initialize the application when the DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    // Make sure we only create one instance
    if (!window.appInstance) {
      window.appInstance = new ConnectivityTroubleshooter();
    }
  });
})();
