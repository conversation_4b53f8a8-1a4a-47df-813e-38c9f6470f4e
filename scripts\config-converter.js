#!/usr/bin/env node

/**
 * Configuration Converter for Bryzos Connectivity Troubleshooter
 * Converts JSON configuration files to Base64 encoded strings for single external config
 */

const fs = require('fs');
const path = require('path');

// Base64 encoding utility
function encodeConfig(data) {
  return Buffer.from(JSON.stringify(data)).toString('base64');
}

// Configuration file paths
const configDir = path.join(__dirname, '..', 'src', 'config');
const rootDir = path.join(__dirname, '..');

console.log('🔧 Bryzos Configuration Converter (Single Config Mode)');
console.log('=====================================================\n');

// Check which config file to use (default to prod)
const configFile = process.argv[2] || 'prod';
let sourceFile;

// Try different locations for the config file
if (configFile.endsWith('.json')) {
  // Direct JSON file path
  sourceFile = path.join(rootDir, configFile);
} else {
  // Look for .config.json in src/config first, then root
  sourceFile = path.join(configDir, `${configFile}.config.json`);
  if (!fs.existsSync(sourceFile)) {
    sourceFile = path.join(rootDir, `${configFile}.json`);
  }
}

const targetFile = path.join(rootDir, 'bz.config');

if (!fs.existsSync(sourceFile)) {
  console.error(`❌ Configuration file not found: ${sourceFile}`);
  console.log('\n📋 Available configurations:');

  // Check src/config directory
  if (fs.existsSync(configDir)) {
    const configFiles = fs
      .readdirSync(configDir)
      .filter((f) => f.endsWith('.config.json'));
    if (configFiles.length > 0) {
      console.log('   From src/config/:');
      configFiles.forEach((file) => {
        console.log(`   - ${file.replace('.config.json', '')}`);
      });
    }
  }

  // Check root directory
  const rootFiles = fs
    .readdirSync(rootDir)
    .filter((f) => f.endsWith('.json') && f !== 'package.json');
  if (rootFiles.length > 0) {
    console.log('   From root directory:');
    rootFiles.forEach((file) => {
      console.log(`   - ${file}`);
    });
  }

  process.exit(1);
}

try {
  // Read and encode the configuration
  const config = JSON.parse(fs.readFileSync(sourceFile, 'utf8'));
  const encoded = encodeConfig(config);

  // Write to bz.config file
  fs.writeFileSync(targetFile, encoded, 'utf8');

  console.log(
    `✅ Successfully created bz.config from ${configFile}.config.json`
  );
  console.log(`📁 Source: ${sourceFile}`);
  console.log(`📄 Target: ${targetFile}`);
  console.log(`🔢 Endpoints: ${config.length}`);
  console.log(`📏 Base64 Length: ${encoded.length} characters`);

  console.log('\n📋 Configuration Summary:');
  config.forEach((endpoint, index) => {
    console.log(
      `   ${index + 1}. ${endpoint.displayName} (${endpoint.id}) - ${
        endpoint.method
      }`
    );
  });

  console.log('\n🚀 Deployment Instructions:');
  console.log('1. Copy the bz.config file to your target machine');
  console.log('2. Place it in the same directory as your application');
  console.log('3. Restart the application to load the new configuration');
  console.log('4. No rebuild required - just replace the config file!');
} catch (error) {
  console.error(`❌ Error processing configuration:`, error.message);
  process.exit(1);
}
