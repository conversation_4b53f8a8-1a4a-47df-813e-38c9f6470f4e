#!/bin/bash

# Bryzos Connectivity Troubleshooter - Launch Script

echo "🚀 Starting Bryzos Connectivity Troubleshooter..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies."
        exit 1
    fi
fi

# Launch the application
echo "🔧 Launching application..."
npm run dev

echo "✅ Application closed."
